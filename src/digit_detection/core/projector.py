"""Visualizer module for dimensionality reduction and projection visualization."""

from dataclasses import dataclass
from enum import Enum, auto
from typing import Dict, List, Literal, Optional, Union, Any, <PERSON><PERSON>

import numpy as np
from numpy.typing import NDArray
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
# Import needed for 3D projection even if not directly referenced
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import umap

from digit_detection.custom_nn.epoch_data import EpochData


class ProjectionMethod(Enum):
    """Supported projection methods."""
    PCA = auto()
    TSNE = auto()
    UMAP = auto()


class ProjectionTarget(Enum):
    """Target data for projection."""
    INPUT = auto()
    OUTPUT = auto()


class ProjectionDimension(Enum):
    """Projection dimensionality."""
    TWO_D = auto()
    THREE_D = auto()

@dataclass
class ProjectionData:
    projected_data: NDArray[np.float64]
    labels: NDArray[np.int64]
    preds: NDArray[np.int64]
    accuracy: float
    epoch: int

class Projector:
    """Visualizer for dimensionality reduction and projection visualization."""

    def create_projection_data(
        self,
        epoch_data: List[EpochData],
        target: ProjectionTarget,
        method: ProjectionMethod,
        dimension: ProjectionDimension,
        sample_count: Optional[int] = None,
        random_seed: int = 42
    ) -> List[ProjectionData]:
        """Create projection data for visualization.

        Args:
            epoch_data: List of epoch data
            target: Projection target (INPUT or OUTPUT)
            method: Projection method (PCA, TSNE, or UMAP)
            dimension: Projection dimension (TWO_D or THREE_D)
            sample_count: Optional number of samples to use

        Returns:
            List of projection data
        """

        np.random.seed(random_seed)
        
        projection_data = []
        for epoch, epoch_data in enumerate(epoch_data):

            # Select data based on target
            high_dimensional_data = epoch_data.test_input if target == ProjectionTarget.INPUT else epoch_data.test_output_actual
            labels = epoch_data.test_output_expected
            predicted_labels = np.argmax(epoch_data.test_output_actual, axis=1)

            # Apply randomized sampling if sample_count is specified
            if sample_count is not None:
                sample_indices = self.__get_random_sample_indices(len(high_dimensional_data), sample_count)
                high_dimensional_data = high_dimensional_data[sample_indices]
                labels = labels[sample_indices]
                predicted_labels = predicted_labels[sample_indices]

            projected_data = self.__compute_projection(method, high_dimensional_data, dimension)

            projection_data.append(
                ProjectionData(
                    projected_data=projected_data,
                    labels=labels,
                    preds=predicted_labels,
                    accuracy=epoch_data.accuracy,
                    epoch=epoch
                )
            )

        return projection_data

    def __get_random_sample_indices(self, total_samples: int, sample_count: int) -> NDArray[np.int64]:
        """Get random sample indices for sampling.

        Args:
            total_samples: Total number of available samples
            sample_count: Number of samples to select

        Returns:
            Array of random indices
        """
        # Ensure we don't sample more than available
        actual_sample_count = min(sample_count, total_samples)

        # Generate random indices without replacement
        return np.random.choice(total_samples, size=actual_sample_count, replace=False)

    def __compute_projection(
        self,
        method: ProjectionMethod,
        data: NDArray[np.float64],
        dimension: ProjectionDimension = ProjectionDimension.TWO_D
    ) -> NDArray[np.float64]:
        """Compute projection using the specified method.

        Args:
            method: Projection method
            data: Data to project
            dimension: Projection dimension (TWO_D or THREE_D)

        Returns:
            Projected data
        """
        n_components = 3 if dimension == ProjectionDimension.THREE_D else 2

        if method == ProjectionMethod.PCA:
            reducer = PCA(n_components=n_components)
        elif method == ProjectionMethod.TSNE:
            reducer = TSNE(n_components=n_components, perplexity=30, random_state=42)
        elif method == ProjectionMethod.UMAP:
            reducer = umap.UMAP(n_components=n_components, random_state=42)
        else:
            raise ValueError(f"Unsupported projection method: {method}")

        # Cast the result to ensure correct type annotation
        result: NDArray[np.float64] = reducer.fit_transform(data)
        return result
