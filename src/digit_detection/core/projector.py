"""Visualizer module for dimensionality reduction and projection visualization."""

from dataclasses import dataclass
from enum import Enum, auto
from typing import Dict, List, Literal, Optional, Union, Any, <PERSON><PERSON>

import numpy as np
from numpy.typing import NDArray
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
# Import needed for 3D projection even if not directly referenced
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import umap

from digit_detection.custom_nn.epoch_data import EpochData


class ProjectionMethod(Enum):
    """Supported projection methods."""
    PCA = auto()
    TSNE = auto()
    UMAP = auto()


class ProjectionTarget(Enum):
    """Target data for projection."""
    INPUT = auto()
    OUTPUT = auto()


class ProjectionDimension(Enum):
    """Projection dimensionality."""
    TWO_D = auto()
    THREE_D = auto()

@dataclass
class ProjectionData:
    projected_data: NDArray[np.float64]
    labels: NDArray[np.int64]
    preds: NDArray[np.int64]
    accuracy: float
    epoch: int

class Projector:
    """Visualizer for dimensionality reduction and projection visualization."""

    def create_projection_data(
        self,
        epoch_data: List[EpochData],
        target: ProjectionTarget,
        method: ProjectionMethod,
        dimension: ProjectionDimension,
        sample_count: Optional[int] = None
    ) -> List[ProjectionData]:
        """Create projection data for visualization.

        Args:
            epoch_data: List of epoch data
            target: Projection target (INPUT or OUTPUT)
            method: Projection method (PCA, TSNE, or UMAP)
            dimension: Projection dimension (TWO_D or THREE_D)
            sample_count: Optional number of samples to use

        Returns:
            List of projection data
        """

        projection_data = []
        for epoch, epoch_data in enumerate(epoch_data):
            
            # Select data based on target
            high_dimensional_data = epoch_data.test_input if target == ProjectionTarget.INPUT else epoch_data.test_output_actual
            if sample_count is not None:
                high_dimensional_data = high_dimensional_data[:sample_count]
            
            # Select labels and predicted labels
            labels = epoch_data.test_output_expected
            if sample_count is not None:
                labels = labels[:sample_count]

            predicted_labels = np.argmax(epoch_data.test_output_actual, axis=1)
            if sample_count is not None:
                predicted_labels = predicted_labels[:sample_count]

            projected_data = self.__compute_projection(method, high_dimensional_data, dimension)
            
            projection_data.append(
                ProjectionData(
                    projected_data=projected_data,
                    labels=labels,
                    preds=predicted_labels,
                    accuracy=epoch_data.accuracy,
                    epoch=epoch
                )
            )

        return projection_data

    def __compute_projection(
        self,
        method: ProjectionMethod,
        data: NDArray[np.float64],
        dimension: ProjectionDimension = ProjectionDimension.TWO_D
    ) -> NDArray[np.float64]:
        """Compute projection using the specified method.

        Args:
            method: Projection method
            data: Data to project
            dimension: Projection dimension (TWO_D or THREE_D)

        Returns:
            Projected data
        """
        n_components = 3 if dimension == ProjectionDimension.THREE_D else 2

        if method == ProjectionMethod.PCA:
            reducer = PCA(n_components=n_components)
        elif method == ProjectionMethod.TSNE:
            reducer = TSNE(n_components=n_components, perplexity=30, random_state=42)
        elif method == ProjectionMethod.UMAP:
            reducer = umap.UMAP(n_components=n_components, random_state=42)
        else:
            raise ValueError(f"Unsupported projection method: {method}")

        # Cast the result to ensure correct type annotation
        result: NDArray[np.float64] = reducer.fit_transform(data)
        return result

    @classmethod
    def from_epoch_data(
        cls,
        epoch_data: List[EpochData],
        model: Any,
        feature_model: Optional[Any] = None,
    ) -> "Projector":
        """Create visualizer from epoch data.

        Args:
            epoch_data: List of epoch data
            model: Trained model for predictions
            feature_model: Optional feature extraction model

        Returns:
            Visualizer instance
        """
        # Use the last epoch data for initialization
        last_epoch = epoch_data[-1]

        return cls(
            x_raw=last_epoch.test_input,
            y_true=last_epoch.test_output_expected,
            model=model,
            feature_model=feature_model,
        )
