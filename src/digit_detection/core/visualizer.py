from digit_detection.core.projector import ProjectionData
from typing import List, Optional
import numpy as np
import tempfile
from pathlib import Path
from manim import (
    Scene, VGroup, Dot, Text, Axes, FadeIn, FadeOut, always_redraw, config,
    RED, GREEN, BLUE, GOLD, PURPLE, ORANGE, TEAL, PINK, MAROON, GRAY, WHITE, YELLOW, BLACK,
    UL, UR, DR, DOWN, RIGHT, UP, Rectangle
)

class Visualizer:

    def __init__(self, projection_data_frames: list[ProjectionData] = None, use_3d: bool = False, **kwargs):
        """
        Initializes the scene with a list of projection data frames.

        Args:
            projection_data_frames (list[ProjectionData], optional):
                A list of ProjectionData objects, each representing a frame/epoch.
                Defaults to an empty list.
            use_3d (bool, optional): Whether to interpret projected_data as 3D.
                                     Currently, the scene is set up for 2D visualization.
                                     Defaults to False.
        """
        super().__init__(**kwargs)
        self.all_data = projection_data_frames if projection_data_frames is not None else []
        self.use_3d = use_3d # This scene is primarily designed for 2D projection visualization

class ModelTrainingScene(Scene):
    """Manim scene for visualizing neural network training progress through projection data."""

    # Class constants
    DIGIT_COLORS = [RED, GREEN, BLUE, GOLD, PURPLE, ORANGE, TEAL, PINK, MAROON, GRAY]
    DOT_RADIUS = 0.04
    TEXT_SIZE = 12
    ANIMATION_RUNTIME = 1.5
    WAIT_TIME = 0.5
    FINAL_WAIT_TIME = 1.0
    SCALE_FACTOR = 3.0  # Reduced from 5.0 to leave more margin
    CORRECT_ALPHA_FADE = 1.0
    # Margins to avoid overlap with UI elements - 4 quadrant layout
    LEFT_MARGIN = 0.5   # Small margin for statistics table
    RIGHT_MARGIN = 3.5  # Space for accuracy graph and confusion matrix
    TOP_MARGIN = 1.5    # Space for headers and statistics
    BOTTOM_MARGIN = 2.0 # Space for confusion matrix

    def __init__(self, projection_data: List[ProjectionData], use_3d: bool = False, **kwargs):
        """Initialize the scene with projection data.

        Args:
            projection_data: List of projection data for each epoch
            use_3d: Whether to use 3D projection
            **kwargs: Additional arguments for Scene
        """
        super().__init__(**kwargs)
        self._validate_input_data(projection_data)
        self.projection_data = projection_data
        self.use_3d = use_3d
        self.scale_factor, self.center_x, self.center_y = self._calculate_scale_factor()

    def _validate_input_data(self, projection_data: List[ProjectionData]) -> None:
        """Validate the input projection data.

        Args:
            projection_data: List of projection data to validate

        Raises:
            ValueError: If projection data is empty or invalid
        """
        if not projection_data:
            raise ValueError("Projection data cannot be empty")

        if not all(isinstance(data, ProjectionData) for data in projection_data):
            raise ValueError("All items must be ProjectionData instances")

    def _calculate_scale_factor(self) -> tuple[float, np.ndarray, np.ndarray]:
        """Calculate the scale factor and centering for normalizing projection coordinates.

        Returns:
            Tuple of (scale_factor, center_x, center_y) for coordinate normalization
        """
        all_points = np.concatenate([data.projected_data for data in self.projection_data], axis=0)

        # Calculate the range and center for each dimension
        x_min, x_max = np.min(all_points[:, 0]), np.max(all_points[:, 0])
        y_min, y_max = np.min(all_points[:, 1]), np.max(all_points[:, 1])

        x_range = x_max - x_min
        y_range = y_max - y_min

        # Center points
        center_x = (x_min + x_max) / 2
        center_y = (y_min + y_max) / 2

        # Scale to use available screen space, accounting for UI elements and margins
        # Calculate available space after accounting for margins
        available_width = 14.0 - self.LEFT_MARGIN - self.RIGHT_MARGIN  # Manim default scene width is ~14
        available_height = 8.0 - self.TOP_MARGIN - self.BOTTOM_MARGIN  # Manim default scene height is ~8

        max_range = max(x_range, y_range)
        # Use the smaller of the available dimensions to ensure points fit
        available_space = min(available_width, available_height)
        scale_factor = available_space / max_range if max_range > 0 else 1.0

        return scale_factor, center_x, center_y

    def _create_projection_axes(self) -> VGroup:
        """Create coordinate axes for the main projection visualization area with header.

        Returns:
            VGroup containing axes and header for the projection space
        """
        # Calculate the range for the axes based on the scaled data
        # Use a slightly larger range than the actual data to provide context
        all_points = np.concatenate([data.projected_data for data in self.projection_data], axis=0)

        x_min, x_max = np.min(all_points[:, 0]), np.max(all_points[:, 0])
        y_min, y_max = np.min(all_points[:, 1]), np.max(all_points[:, 1])

        # Add some padding to the axes range
        x_range = x_max - x_min
        y_range = y_max - y_min
        x_padding = x_range * 0.1
        y_padding = y_range * 0.1

        # Convert to scene coordinates for axis range
        axis_x_min = (x_min - x_padding - self.center_x) * self.scale_factor
        axis_x_max = (x_max + x_padding - self.center_x) * self.scale_factor
        axis_y_min = (y_min - y_padding - self.center_y) * self.scale_factor
        axis_y_max = (y_max + y_padding - self.center_y) * self.scale_factor

        # Apply the same margin offsets as the data points
        margin_offset_x = (self.LEFT_MARGIN - self.RIGHT_MARGIN) / 2
        margin_offset_y = (self.BOTTOM_MARGIN - self.TOP_MARGIN) / 2

        axes = Axes(
            x_range=[axis_x_min, axis_x_max, (axis_x_max - axis_x_min) / 5],
            y_range=[axis_y_min, axis_y_max, (axis_y_max - axis_y_min) / 5],
            x_length=abs(axis_x_max - axis_x_min),
            y_length=abs(axis_y_max - axis_y_min),
            axis_config={
                "color": GRAY,
                "stroke_width": 1,
                "include_numbers": False,  # Don't show numbers to keep it clean
            },
            tips=False
        ).shift([margin_offset_x, margin_offset_y, 0])

        # Create header for projection area
        projection_header = Text("Projection", font_size=14, color=WHITE)
        projection_header.to_corner(DOWN + RIGHT, buff=0.3)  # Position in bottom left area

        # Group axes and header
        projection_group = VGroup(axes, projection_header)

        return projection_group

    def _create_accuracy_graph(self) -> VGroup:
        """Create the accuracy tracking graph with dynamic y-axis range and header.

        Returns:
            VGroup containing the accuracy graph with header
        """
        epochs = len(self.projection_data)

        # Calculate dynamic y-axis range based on accuracy values
        all_accuracies = [data.accuracy for data in self.projection_data]
        min_acc = min(all_accuracies)
        max_acc = max(all_accuracies)

        # Add padding for better visualization
        acc_range = max_acc - min_acc
        padding = max(0.05, acc_range * 0.1)  # At least 5% padding or 10% of range

        y_min = max(0.0, min_acc - padding)
        y_max = min(1.0, max_acc + padding)

        # Calculate appropriate step size
        y_range_span = y_max - y_min
        if y_range_span <= 0.2:
            y_step = 0.02
        elif y_range_span <= 0.5:
            y_step = 0.05
        else:
            y_step = 0.1

        # Create smaller axes
        axes = Axes(
            x_range=[0, epochs, max(1, epochs // 10)],
            y_range=[y_min, y_max, y_step],
            x_length=2.5,  # Smaller size
            y_length=2.0,  # Smaller size
            axis_config={"font_size": 10},
            y_axis_config = {
                "include_numbers": True,
                "label_constructor": lambda x: Text(x, font_size=12)
            },
            tips=False
        )

        # Create header
        header = Text("Accuracy", font_size=14, color=WHITE)
        header.next_to(axes, UP, buff=0.1)

        # Group axes and header
        graph_group = VGroup(header, axes)  # Header first, then axes
        graph_group.to_corner(UR, buff=0.3)

        return graph_group

    def _convert_to_scene_coordinates(self, point: np.ndarray) -> list:
        """Convert projection coordinates to scene coordinates with proper centering and margins.

        Args:
            point: Projection coordinates

        Returns:
            Scene coordinates as [x, y, z] list
        """
        # Center and scale the point
        centered_x = (point[0] - self.center_x) * self.scale_factor
        centered_y = (point[1] - self.center_y) * self.scale_factor

        # Apply margins to keep points away from UI elements
        # Shift the visualization area away from UI elements
        # Move right to avoid legend (left side) and left to avoid accuracy graph (right side)
        margin_offset_x = (self.LEFT_MARGIN - self.RIGHT_MARGIN) / 2  # Negative value shifts left
        margin_offset_y = (self.BOTTOM_MARGIN - self.TOP_MARGIN) / 2  # Negative value shifts down

        final_x = centered_x + margin_offset_x
        final_y = centered_y + margin_offset_y

        if self.use_3d:
            if len(point) >= 3:
                centered_z = (point[2] - 0) * self.scale_factor  # Assume z is already centered
                return [final_x, final_y, centered_z]
            else:
                return [final_x, final_y, 0]
        else:
            return [final_x, final_y, 0]

    def _convert_from_scene_coordinates(self, scene_coords: list) -> np.ndarray:
        """Convert scene coordinates back to projection coordinates.

        Args:
            scene_coords: Scene coordinates as [x, y, z] list

        Returns:
            Projection coordinates as numpy array
        """
        # Remove margin offsets first (reverse the transformation)
        margin_offset_x = (self.LEFT_MARGIN - self.RIGHT_MARGIN) / 2  # Same as forward transformation
        margin_offset_y = (self.BOTTOM_MARGIN - self.TOP_MARGIN) / 2  # Same as forward transformation

        adjusted_x = scene_coords[0] - margin_offset_x
        adjusted_y = scene_coords[1] - margin_offset_y

        # Reverse the centering and scaling
        proj_x = (adjusted_x / self.scale_factor) + self.center_x
        proj_y = (adjusted_y / self.scale_factor) + self.center_y

        if self.use_3d and len(scene_coords) >= 3:
            proj_z = (scene_coords[2] / self.scale_factor) + 0  # Assume z center is 0
            return np.array([proj_x, proj_y, proj_z])
        else:
            return np.array([proj_x, proj_y])

    def _create_correct_prediction_dot(self, position: list, true_label: int) -> Dot:
        """Create a dot visualization for correct predictions.

        Args:
            position: Scene coordinates [x, y, z]
            true_label: True digit label

        Returns:
            Dot object for correct prediction
        """
        return Dot(
            point=position,
            radius=self.DOT_RADIUS,
            color=self.DIGIT_COLORS[true_label],
        ).set_opacity(self.CORRECT_ALPHA_FADE)

    def _create_incorrect_prediction_text(self, position: list, true_label: int, predicted_label: int) -> Text:
        """Create a text visualization for incorrect predictions.

        Args:
            position: Scene coordinates [x, y, z]
            true_label: True digit label
            predicted_label: Predicted digit label

        Returns:
            Text object for incorrect prediction
        """
        return Text(
            str(predicted_label),
            font_size=self.TEXT_SIZE,
            color=self.DIGIT_COLORS[true_label],
        ).move_to(position)

    def _create_data_point_visualization(self, point: np.ndarray, true_label: int, predicted_label: int):
        """Create a visualization for a data point (dot for correct, digit text for incorrect).

        Args:
            point: Projection coordinates
            true_label: True digit label
            predicted_label: Predicted digit label

        Returns:
            Manim object representing the data point
        """
        is_correct = predicted_label == true_label
        scene_coords = self._convert_to_scene_coordinates(point)

        if is_correct:
            return self._create_correct_prediction_dot(scene_coords, true_label)
        else:
            return self._create_incorrect_prediction_text(scene_coords, true_label, predicted_label)

    def _create_initial_visualizations(self) -> VGroup:
        """Create initial visualizations for the first epoch.

        Returns:
            VGroup containing all initial visualizations
        """
        visualizations = VGroup()
        initial_data = self.projection_data[0]

        for i, point in enumerate(initial_data.projected_data):
            true_label = initial_data.labels[i]
            predicted_label = initial_data.preds[i]

            viz = self._create_data_point_visualization(point, true_label, predicted_label)
            visualizations.add(viz)

        return visualizations

    def _create_accuracy_curve(self, acc_graph: Axes, accuracy_values: List[float]) -> VGroup:
        """Create the accuracy curve for the graph.

        Args:
            acc_graph: Axes object for the graph
            accuracy_values: List of accuracy values

        Returns:
            VGroup containing the accuracy curve
        """
        if len(accuracy_values) < 2:
            return VGroup()

        return acc_graph.plot_line_graph(
            x_values=list(range(1, len(accuracy_values) + 1)),
            y_values=accuracy_values,
            add_vertex_dots=True,
            vertex_dot_radius=0.03,
            line_color=YELLOW
        )

    def _create_epoch_text(self, epoch: int, accuracy: float) -> Text:
        """Create epoch information text.

        Args:
            epoch: Current epoch number
            accuracy: Current accuracy value

        Returns:
            Text object displaying epoch information
        """
        return Text(
            f"Epoch {epoch} Accuracy: {accuracy*100:.2f}%",
            font_size=24
        ).to_corner(DR)

    def _calculate_digit_statistics(self, projection_data: ProjectionData) -> dict:
        """Calculate statistics for each digit.

        Args:
            projection_data: Current epoch projection data

        Returns:
            Dictionary with digit statistics
        """
        stats = {}

        for digit in range(10):
            # Count actual occurrences
            actual_count = np.sum(projection_data.labels == digit)

            # Count predicted occurrences
            predicted_count = np.sum(projection_data.preds == digit)

            # Calculate accuracy for this digit
            if actual_count > 0:
                correct_predictions = np.sum(
                    (projection_data.labels == digit) &
                    (projection_data.preds == digit)
                )
                digit_accuracy = correct_predictions / actual_count
            else:
                digit_accuracy = 0.0

            stats[digit] = {
                'actual': actual_count,
                'predicted': predicted_count,
                'accuracy': digit_accuracy
            }

        return stats

    def _create_digit_statistics_table(self, projection_data: ProjectionData) -> VGroup:
        """Create a table showing digit statistics with header.

        Args:
            projection_data: Current epoch projection data

        Returns:
            VGroup containing the statistics table with header
        """
        stats = self._calculate_digit_statistics(projection_data)
        table = VGroup()

        # Table dimensions - smaller
        cell_width = 0.35
        cell_height = 0.25
        font_size = 10
        header_cell_width = 0.6

        # Calculate totals
        total_actual = sum(stats[digit]['actual'] for digit in range(10))
        total_predicted = sum(stats[digit]['predicted'] for digit in range(10))

        # Create header row with row labels and digit numbers
        header_row = VGroup()

        # Empty cell for top-left corner
        empty_cell = Rectangle(width=header_cell_width, height=cell_height, stroke_color=WHITE, stroke_width=1, fill_opacity=0)
        header_row.add(empty_cell)

        # Digit columns
        for digit in range(10):
            header_cell = Rectangle(width=cell_width, height=cell_height, stroke_color=WHITE, stroke_width=1, fill_opacity=0)
            header_text = Text(str(digit), font_size=font_size, color=self.DIGIT_COLORS[digit]).move_to(header_cell.get_center())
            header_group = VGroup(header_cell, header_text)
            header_group.next_to(header_row, RIGHT, buff=0)
            header_row.add(header_group)

        # Total column
        total_header_cell = Rectangle(width=cell_width, height=cell_height, stroke_color=WHITE, stroke_width=1, fill_opacity=0)
        total_header_text = Text("Total", font_size=font_size, color=WHITE).move_to(total_header_cell.get_center())
        total_header_group = VGroup(total_header_cell, total_header_text)
        total_header_group.next_to(header_row, RIGHT, buff=0)
        header_row.add(total_header_group)

        table.add(header_row)

        # Create actual count row
        actual_row = VGroup()

        # Row label
        actual_label_cell = Rectangle(width=header_cell_width, height=cell_height, stroke_color=WHITE, stroke_width=1, fill_opacity=0)
        actual_label_text = Text("Actual", font_size=font_size, color=WHITE).move_to(actual_label_cell.get_center())
        actual_label_group = VGroup(actual_label_cell, actual_label_text)
        actual_row.add(actual_label_group)

        # Digit columns
        for digit in range(10):
            actual_cell = Rectangle(width=cell_width, height=cell_height, stroke_color=WHITE, stroke_width=1, fill_opacity=0)
            actual_text = Text(str(stats[digit]['actual']), font_size=font_size, color=WHITE).move_to(actual_cell.get_center())
            actual_group = VGroup(actual_cell, actual_text)
            actual_group.next_to(actual_row, RIGHT, buff=0)
            actual_row.add(actual_group)

        # Total column
        actual_total_cell = Rectangle(width=cell_width, height=cell_height, stroke_color=WHITE, stroke_width=1, fill_opacity=0)
        actual_total_text = Text(str(total_actual), font_size=font_size, color=WHITE).move_to(actual_total_cell.get_center())
        actual_total_group = VGroup(actual_total_cell, actual_total_text)
        actual_total_group.next_to(actual_row, RIGHT, buff=0)
        actual_row.add(actual_total_group)

        actual_row.next_to(header_row, DOWN, buff=0)
        table.add(actual_row)

        # Create predicted count row
        predicted_row = VGroup()

        # Row label
        pred_label_cell = Rectangle(width=header_cell_width, height=cell_height, stroke_color=WHITE, stroke_width=1, fill_opacity=0)
        pred_label_text = Text("Predicted", font_size=font_size, color=WHITE).move_to(pred_label_cell.get_center())
        pred_label_group = VGroup(pred_label_cell, pred_label_text)
        predicted_row.add(pred_label_group)

        # Digit columns
        for digit in range(10):
            predicted_cell = Rectangle(width=cell_width, height=cell_height, stroke_color=WHITE, stroke_width=1, fill_opacity=0)
            predicted_text = Text(str(stats[digit]['predicted']), font_size=font_size, color=WHITE).move_to(predicted_cell.get_center())
            predicted_group = VGroup(predicted_cell, predicted_text)
            predicted_group.next_to(predicted_row, RIGHT, buff=0)
            predicted_row.add(predicted_group)

        # Total column
        pred_total_cell = Rectangle(width=cell_width, height=cell_height, stroke_color=WHITE, stroke_width=1, fill_opacity=0)
        pred_total_text = Text(str(total_predicted), font_size=font_size, color=WHITE).move_to(pred_total_cell.get_center())
        pred_total_group = VGroup(pred_total_cell, pred_total_text)
        pred_total_group.next_to(predicted_row, RIGHT, buff=0)
        predicted_row.add(pred_total_group)

        predicted_row.next_to(actual_row, DOWN, buff=0)
        table.add(predicted_row)

        # Add table header
        table_header = Text("Statistics", font_size=12, color=WHITE)
        table_header.next_to(table, UP, buff=0.1)

        # Group table with header
        table_with_header = VGroup(table_header, table)
        table_with_header.to_corner(UL, buff=0.3)

        return table_with_header

    def _create_confusion_matrix(self, projection_data: ProjectionData) -> VGroup:
        """Create a small confusion matrix visualization.

        Args:
            projection_data: Current epoch projection data

        Returns:
            VGroup containing the confusion matrix with header
        """
        # Calculate confusion matrix
        confusion_matrix = np.zeros((10, 10), dtype=int)
        for true_label, pred_label in zip(projection_data.labels, projection_data.preds):
            confusion_matrix[true_label, pred_label] += 1

        matrix_group = VGroup()

        # Matrix dimensions - very small
        cell_size = 0.15
        font_size = 8

        # Create matrix cells
        for i in range(10):
            for j in range(10):
                cell = Rectangle(
                    width=cell_size,
                    height=cell_size,
                    stroke_color=WHITE,
                    stroke_width=0.5,
                    fill_opacity=0.1 if confusion_matrix[i, j] > 0 else 0,
                    fill_color=WHITE if i == j else RED  # Diagonal vs off-diagonal
                )

                # Position cell
                cell.move_to([j * cell_size, -i * cell_size, 0])

                # Add count text if non-zero
                if confusion_matrix[i, j] > 0:
                    count_text = Text(
                        str(confusion_matrix[i, j]),
                        font_size=font_size,
                        color=BLACK if confusion_matrix[i, j] > 5 else WHITE
                    ).move_to(cell.get_center())
                    cell_group = VGroup(cell, count_text)
                else:
                    cell_group = VGroup(cell)

                matrix_group.add(cell_group)

        # Add header
        matrix_header = Text("Confusion Matrix", font_size=12, color=WHITE)
        matrix_header.next_to(matrix_group, UP, buff=0.1)

        # Group matrix with header
        matrix_with_header = VGroup(matrix_header, matrix_group)
        matrix_with_header.to_corner(DR, buff=0.3)

        return matrix_with_header

    def _create_epoch_text(self, epoch: int) -> Text:
        """Create epoch number text.

        Args:
            epoch: Current epoch number

        Returns:
            Text object displaying epoch number
        """
        return Text(
            f"Epoch {epoch}",
            font_size=16,
            color=WHITE
        ).to_corner(DOWN, buff=0.1)

    def _update_visualization_for_epoch(self, old_viz, point: np.ndarray, true_label: int,
                                       predicted_label: int):
        """Update a visualization for a new epoch, potentially changing type.

        Args:
            old_viz: Previous visualization object
            point: New projection coordinates
            true_label: True digit label
            predicted_label: Predicted digit label

        Returns:
            Tuple of (new_visualization, type_change_animation, position_animation)
        """
        is_correct = predicted_label == true_label
        scene_coords = self._convert_to_scene_coordinates(point)

        # Check if we need to change visualization type
        old_is_dot = isinstance(old_viz, Dot)
        new_should_be_dot = is_correct

        if old_is_dot:

            if new_should_be_dot:
                # Dot to dot, just update position
                return old_viz, None, old_viz.animate.move_to(scene_coords)
            else:
                # Dot to text, create new text at current position
                current_position = old_viz.get_center()
                new_viz = self._create_incorrect_prediction_text(current_position, true_label, predicted_label)
                return new_viz, [FadeOut(old_viz), FadeIn(new_viz)], new_viz.animate.move_to(scene_coords)
        else:

            if new_should_be_dot:
                # Text to dot, create new dot at current position
                current_position = old_viz.get_center()
                new_viz = self._create_correct_prediction_dot(current_position, true_label)
                return new_viz, [FadeOut(old_viz), FadeIn(new_viz)], new_viz.animate.move_to(scene_coords)
            else:
                return old_viz, None, old_viz.animate.move_to(scene_coords)

    def _animate_epoch_transition(self, visualizations: VGroup, epoch_data: ProjectionData) -> tuple[VGroup, List, List]:
        """Create animations for transitioning to a new epoch.

        Args:
            visualizations: VGroup of visualizations to animate
            epoch_data: Data for the current epoch

        Returns:
            Tuple of (updated_visualizations, type_change_animations, position_animations)
        """
        type_change_animations = []
        position_animations = []
        new_visualizations = VGroup()

        for i, viz in enumerate(visualizations):
            point = epoch_data.projected_data[i]
            true_label = epoch_data.labels[i]
            predicted_label = epoch_data.preds[i]

            new_viz, type_change_anim, position_anim = self._update_visualization_for_epoch(
                viz, point, true_label, predicted_label
            )

            new_visualizations.add(new_viz)

            if type_change_anim is not None:
                # Type changed, add type change animations
                type_change_animations.extend(type_change_anim)

            if position_anim is not None:
                # Add position animation
                position_animations.append(position_anim)

        return new_visualizations, type_change_animations, position_animations

    def construct(self) -> None:
        """Construct the complete animation sequence."""
        # Setup phase - create all UI elements
        accuracy_graph = self._create_accuracy_graph()
        self.add(accuracy_graph)

        # Create initial statistics table
        initial_stats_table = self._create_digit_statistics_table(self.projection_data[0])
        self.add(initial_stats_table)

        # Create initial confusion matrix
        initial_confusion_matrix = self._create_confusion_matrix(self.projection_data[0])
        self.add(initial_confusion_matrix)

        # Create initial epoch text
        initial_epoch_text = self._create_epoch_text(self.projection_data[0].epoch)
        self.add(initial_epoch_text)

        # Add projection axes to provide coordinate reference
        projection_axes = self._create_projection_axes()
        self.add(projection_axes)

        # Initialize visualizations and show them
        visualizations = self._create_initial_visualizations()
        self.play(FadeIn(visualizations))
        self.wait(self.WAIT_TIME)

        # Setup accuracy tracking
        accuracy_values = [self.projection_data[0].accuracy]
        # Get the axes from the accuracy graph group (axes is the second element, header is first)
        accuracy_axes = accuracy_graph[1]  # Use indexing instead of submobjects
        accuracy_curve = always_redraw(
            lambda: self._create_accuracy_curve(accuracy_axes, accuracy_values)
        )
        self.add(accuracy_curve)

        # Animate through epochs
        self._animate_training_epochs(
            visualizations,
            accuracy_values,
            initial_stats_table,
            initial_confusion_matrix,
            initial_epoch_text
        )

        # Final pause
        self.wait(self.FINAL_WAIT_TIME)

    def _animate_training_epochs(
        self,
        visualizations: VGroup,
        accuracy_values: List[float],
        stats_table: VGroup,
        confusion_matrix: VGroup,
        epoch_text: Text
    ) -> None:
        """Animate through all training epochs.

        Args:
            visualizations: VGroup of visualizations to animate
            accuracy_values: List to track accuracy values (modified in place)
            stats_table: Statistics table to update
            confusion_matrix: Confusion matrix to update
            epoch_text: Epoch text to update
        """
        current_visualizations = visualizations
        current_stats_table = stats_table
        current_confusion_matrix = confusion_matrix
        current_epoch_text = epoch_text

        for epoch_index in range(1, len(self.projection_data)):
            current_epoch_data = self.projection_data[epoch_index]
            accuracy_values.append(current_epoch_data.accuracy)

            # Create new UI elements for this epoch
            new_stats_table = self._create_digit_statistics_table(current_epoch_data)
            new_confusion_matrix = self._create_confusion_matrix(current_epoch_data)
            new_epoch_text = self._create_epoch_text(current_epoch_data.epoch)

            # Create animations for this epoch
            new_visualizations, type_change_animations, position_animations = self._animate_epoch_transition(
                current_visualizations, current_epoch_data
            )

            # Phase 1: Handle type changes (dot to text or vice versa) and update UI elements
            if type_change_animations:
                # Play type change animations first with UI updates
                type_change_with_updates = type_change_animations + [
                    FadeOut(current_stats_table),
                    FadeIn(new_stats_table),
                    FadeOut(current_confusion_matrix),
                    FadeIn(new_confusion_matrix),
                    FadeOut(current_epoch_text),
                    FadeIn(new_epoch_text)
                ]
                self.play(*type_change_with_updates, run_time=self.ANIMATION_RUNTIME / 2)
                self.wait(self.WAIT_TIME / 2)

                # Phase 2: Handle position changes
                if position_animations:
                    self.play(*position_animations, run_time=self.ANIMATION_RUNTIME / 2)
            else:
                # No type changes, just position animations and UI updates
                all_animations = position_animations + [
                    FadeOut(current_stats_table),
                    FadeIn(new_stats_table),
                    FadeOut(current_confusion_matrix),
                    FadeIn(new_confusion_matrix),
                    FadeOut(current_epoch_text),
                    FadeIn(new_epoch_text)
                ]
                if all_animations:
                    self.play(*all_animations, run_time=self.ANIMATION_RUNTIME)

            self.wait(self.WAIT_TIME)

            # Update current elements for next iteration
            current_visualizations = new_visualizations
            current_stats_table = new_stats_table
            current_confusion_matrix = new_confusion_matrix
            current_epoch_text = new_epoch_text


def render_training_scene(
    projection_data: List[ProjectionData],
    output_file: Optional[str] = None,
    quality: str = "medium",
    use_3d: bool = False
) -> str:
    """Render the training scene and return the video file path.

    Args:
        projection_data: List of projection data for each epoch
        output_file: Output file path (optional, will generate if None)
        quality: Video quality ('low', 'medium', 'high')
        use_3d: Whether to use 3D visualization

    Returns:
        Path to the generated video file
    """
    if not projection_data:
        raise ValueError("Projection data cannot be empty")

    # Generate output file if not provided
    if output_file is None:
        temp_dir = Path(tempfile.gettempdir()) / "manim_videos"
        temp_dir.mkdir(exist_ok=True)
        output_file = str(temp_dir / f"training_animation_{len(projection_data)}_epochs.mp4")

    # Ensure output directory exists
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Configure Manim
    config.output_file = str(output_file)
    config.verbosity = "WARNING"
    config.disable_caching = True

    if quality == "low":
        config.pixel_height = 480
        config.pixel_width = 854
        config.frame_rate = 15
    elif quality == "medium":
        config.pixel_height = 720
        config.pixel_width = 1280
        config.frame_rate = 24
    elif quality == "high":
        config.pixel_height = 1080
        config.pixel_width = 1920
        config.frame_rate = 30
    else:
        raise ValueError(f"Unsupported quality: {quality}")

    # Create and render scene
    scene = ModelTrainingScene(
        projection_data=projection_data,
        use_3d=use_3d,
    )
    scene.render()

    return str(output_file)


def create_streamlit_video(
    projection_data: List[ProjectionData],
    use_3d: bool = False,
    quality: str = "medium"
) -> str:
    """Create a video optimized for Streamlit display.

    Args:
        projection_data: List of projection data for each epoch
        use_3d: Whether to use 3D visualization
        quality: Video quality ('low', 'medium', 'high')

    Returns:
        Path to the generated video file
    """
    # Create output directory in temp folder
    temp_dir = Path(tempfile.gettempdir()) / "streamlit_videos"
    temp_dir.mkdir(exist_ok=True)

    output_file = temp_dir / "training_visualization.mp4"

    return render_training_scene(
        projection_data=projection_data,
        output_file=str(output_file),
        quality=quality,
        use_3d=use_3d,
    )